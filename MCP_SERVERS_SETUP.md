# MCP Servers Setup for Augment Code

## Installed Servers

### 1. Brave Search Server
- **Package**: `@modelcontextprotocol/server-brave-search`
- **Purpose**: Web search capabilities using Brave Search API
- **API Key**: BSA-PnN-C3OxvJq73Adf0B1qgwYOCTL
- **Status**: ✅ Installed and configured

### 2. Fetch Server
- **Package**: `mcp-server-fetch-typescript`
- **Purpose**: Web content fetching and conversion capabilities
- **Status**: ✅ Installed and configured

### 3. Filesystem Server
- **Package**: `@modelcontextprotocol/server-filesystem`
- **Purpose**: Filesystem operations for the current project
- **Allowed Directory**: `/Users/<USER>/Documents/DEV PROJECTS/PAWPUMPS`
- **Status**: ✅ Installed and configured

## Configuration Location

The MCP servers are configured in:
```
~/Library/Application Support/augment/mcp-servers.json
```

## Configuration Details

```json
{
  "servers": [
    {
      "name": "stagewise",
      "command": "node",
      "args": ["/Users/<USER>/Documents/DEV PROJECTS/PAWPUMPS/stagewise-mcp-server/dist/cli.js"],
      "description": "Stagewise browser toolbar integration for frontend development",
      "enabled": true
    },
    {
      "name": "brave-search",
      "command": "npx",
      "args": ["@modelcontextprotocol/server-brave-search"],
      "description": "Brave Search API integration for web search capabilities",
      "enabled": true,
      "env": {
        "BRAVE_API_KEY": "BSA-PnN-C3OxvJq73Adf0B1qgwYOCTL"
      }
    },
    {
      "name": "fetch",
      "command": "npx",
      "args": ["mcp-server-fetch-typescript"],
      "description": "Web content fetching and conversion capabilities",
      "enabled": true
    },
    {
      "name": "filesystem",
      "command": "npx",
      "args": ["@modelcontextprotocol/server-filesystem", "/Users/<USER>/Documents/DEV PROJECTS/PAWPUMPS"],
      "description": "Filesystem operations for the current project",
      "enabled": true
    }
  ]
}
```

## Testing Results

All servers have been tested and are working correctly:

- ✅ **Brave Search**: Server starts successfully with API key
- ✅ **Fetch**: Server starts and responds to MCP protocol
- ✅ **Filesystem**: Server starts with proper directory restrictions

## Next Steps

1. **Restart Augment Code** to load the new MCP servers
2. **Verify in Settings**: Check the MCP servers panel in Augment Code settings
3. **Test Functionality**: Try using the new capabilities:
   - Search the web using Brave Search
   - Fetch content from websites
   - Perform filesystem operations on the project

## Capabilities

### Brave Search Server
- Web search queries
- Real-time search results
- Safe search filtering

### Fetch Server
- HTTP/HTTPS content fetching
- HTML to Markdown conversion
- Content processing for AI consumption

### Filesystem Server
- Read files and directories
- Write files (within allowed directory)
- File metadata operations
- Secure directory restrictions

## Backup

A backup of the original configuration was created at:
```
~/Library/Application Support/augment/mcp-servers.json.backup
```
