#!/usr/bin/env node

/**
 * Test script to verify MCP servers are working
 */

const { spawn } = require('child_process');

async function testServer(name, command, args, env = {}) {
  console.log(`\n🧪 Testing ${name} server...`);
  
  return new Promise((resolve) => {
    const child = spawn(command, args, {
      env: { ...process.env, ...env },
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let errorOutput = '';

    child.stdout.on('data', (data) => {
      output += data.toString();
    });

    child.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    // Send a simple MCP initialization request
    const initRequest = JSON.stringify({
      jsonrpc: "2.0",
      id: 1,
      method: "initialize",
      params: {
        protocolVersion: "2024-11-05",
        capabilities: {},
        clientInfo: {
          name: "test-client",
          version: "1.0.0"
        }
      }
    }) + '\n';

    child.stdin.write(initRequest);

    setTimeout(() => {
      child.kill();
      
      if (output.includes('"result"') || errorOutput.includes('running on stdio')) {
        console.log(`✅ ${name} server is working`);
        if (errorOutput.includes('running on stdio')) {
          console.log(`   Status: ${errorOutput.trim()}`);
        }
      } else {
        console.log(`❌ ${name} server may have issues`);
        if (output) console.log(`   Output: ${output.trim()}`);
        if (errorOutput) console.log(`   Error: ${errorOutput.trim()}`);
      }
      
      resolve();
    }, 3000);
  });
}

async function main() {
  console.log('🔧 Testing MCP Servers Installation\n');

  // Test Brave Search
  await testServer(
    'Brave Search',
    'node',
    ['/opt/homebrew/lib/node_modules/@modelcontextprotocol/server-brave-search/dist/index.js'],
    { BRAVE_API_KEY: 'BSA-PnN-C3OxvJq73Adf0B1qgwYOCTL' }
  );

  // Test Fetch
  await testServer(
    'Fetch',
    'node',
    ['/opt/homebrew/lib/node_modules/mcp-server-fetch-typescript/build/index.js']
  );

  // Test Filesystem
  await testServer(
    'Filesystem',
    'node',
    ['/opt/homebrew/lib/node_modules/@modelcontextprotocol/server-filesystem/dist/index.js', '/Users/<USER>/Documents/DEV PROJECTS/PAWPUMPS']
  );

  console.log('\n🎉 MCP Server testing completed!');
  console.log('\n📋 Next Steps:');
  console.log('1. Restart Augment Code to load the new MCP servers');
  console.log('2. Check the MCP servers panel in Augment Code settings');
  console.log('3. Test the servers by using their capabilities in conversations');
}

main().catch(console.error);
