'use client'

import { useEffect, useState } from 'react'
import dynamic from 'next/dynamic'

// Dynamically import the Stagewise toolbar to avoid SSR issues
const StagewiseToolbar = dynamic(
  () => import('@stagewise/toolbar-next').then(mod => ({ default: mod.StagewiseToolbar })),
  {
    ssr: false,
    loading: () => null
  }
)

export function StagewiseClient() {
  const [isConnected, setIsConnected] = useState(false)
  const [extensionPort, setExtensionPort] = useState<number | null>(null)
  const [showIndicator, setShowIndicator] = useState(false)

  useEffect(() => {
    // Test connection to VS Code extension
    const testConnection = async () => {
      try {
        console.log('🔄 Testing Stagewise extension connection...')

        for (let port = 5746; port <= 5756; port++) {
          try {
            console.log(`Testing port ${port}...`)
            const response = await fetch(`http://localhost:${port}/ping/stagewise`, {
              method: 'GET',
              headers: { 'Accept': 'text/plain' }
            })
            console.log(`Port ${port} response:`, response.status, response.ok)
            if (response.ok) {
              const result = await response.text()
              console.log(`Port ${port} result:`, result)
              if (result === 'stagewise') {
                console.log(`✅ VS Code extension found on port ${port}`)
                setExtensionPort(port)
                setIsConnected(true)
                setShowIndicator(true)

                // Hide indicator after 5 seconds
                setTimeout(() => setShowIndicator(false), 5000)
                return port
              }
            }
          } catch (error) {
            console.log(`Port ${port} failed:`, error.message)
            // Continue to next port
          }
        }
        throw new Error('VS Code extension not found on ports 5746-5756')
      } catch (error) {
        console.error('❌ Failed to connect to Stagewise extension:', error)
        setIsConnected(false)
        setShowIndicator(true)

        // Hide error indicator after 8 seconds
        setTimeout(() => setShowIndicator(false), 8000)
      }
    }

    // Only test connection in development and on localhost
    if (process.env.NODE_ENV === 'development' &&
        typeof window !== 'undefined' &&
        window.location.hostname === 'localhost') {
      console.log('🚀 Development mode detected, testing Stagewise connection...')
      console.log('Current hostname:', window.location.hostname)
      console.log('Current NODE_ENV:', process.env.NODE_ENV)
      testConnection()
    } else {
      console.log('⚠️ Stagewise only available in development mode on localhost')
      console.log('Current hostname:', typeof window !== 'undefined' ? window.location.hostname : 'undefined')
      console.log('Current NODE_ENV:', process.env.NODE_ENV)
    }
  }, [])

  // Only render the toolbar if we're connected and in development
  const shouldRenderToolbar = process.env.NODE_ENV === 'development' &&
                              typeof window !== 'undefined' &&
                              window.location.hostname === 'localhost' &&
                              isConnected

  console.log('🔍 Stagewise render check:', {
    NODE_ENV: process.env.NODE_ENV,
    hostname: typeof window !== 'undefined' ? window.location.hostname : 'undefined',
    isConnected,
    shouldRenderToolbar,
    extensionPort
  })

  return (
    <>
      {/* Connection status indicator */}
      {showIndicator && (
        <div
          style={{
            position: 'fixed',
            top: '10px',
            right: '10px',
            background: isConnected ? '#10b981' : '#ef4444',
            color: 'white',
            padding: '8px 12px',
            borderRadius: '6px',
            fontSize: '12px',
            zIndex: 10000,
            fontFamily: 'monospace',
          }}
        >
          {isConnected
            ? `✅ Stagewise Connected (${extensionPort})`
            : '❌ Stagewise Connection Failed'
          }
        </div>
      )}

      {/* Debug info */}
      <div
        style={{
          position: 'fixed',
          bottom: '10px',
          right: '10px',
          background: '#333',
          color: 'white',
          padding: '8px 12px',
          borderRadius: '6px',
          fontSize: '10px',
          zIndex: 10000,
          fontFamily: 'monospace',
          maxWidth: '300px',
        }}
      >
        Debug: shouldRender={shouldRenderToolbar ? 'YES' : 'NO'}, connected={isConnected ? 'YES' : 'NO'}, port={extensionPort}
      </div>

      {/* Render the Stagewise toolbar if connected */}
      {shouldRenderToolbar && (
        <div style={{ border: '2px solid red', padding: '10px', position: 'fixed', top: '50px', left: '10px', background: 'yellow', zIndex: 10000 }}>
          <div>🔧 Stagewise Toolbar Loading...</div>
          <StagewiseToolbar />
        </div>
      )}

      {/* Always show a test element to verify the component is rendering */}
      <div
        style={{
          position: 'fixed',
          bottom: '50px',
          right: '10px',
          background: '#0066cc',
          color: 'white',
          padding: '4px 8px',
          borderRadius: '4px',
          fontSize: '10px',
          zIndex: 10000,
          fontFamily: 'monospace',
        }}
      >
        StagewiseClient Active
      </div>
    </>
  )
}
